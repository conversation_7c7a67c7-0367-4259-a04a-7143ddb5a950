/* Skills Section */
.skills-section {
  background: #0a0f1c;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

/* Skill Category */
.skill-category {
  background: rgba(15, 20, 32, 0.6);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  transition: all 0.3s ease;
}

.skill-category:hover {
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 5px 20px rgba(0, 212, 255, 0.2);
  transform: translateY(-5px);
}

.skill-category h3 {
  font-size: 1.5rem;
  color: #00d4ff;
  margin-bottom: 25px;
  text-align: center;
}

/* Skills List */
.skills-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.skill-item {
  width: 100%;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.skill-name {
  font-weight: 500;
  color: #ffffff;
}

.skill-percentage {
  color: #00d4ff;
  font-weight: 600;
}

/* Skill Bar */
.skill-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #ff00d4);
  border-radius: 10px;
  position: relative;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .skills-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .skill-category {
    padding: 25px;
  }
}
