import React, { forwardRef, useRef, useEffect } from 'react';
import { useGLTF, useTexture } from '@react-three/drei';
import { Group, DoubleSide, LessEqualDepth, CustomBlending, OneFactor, AddEquation } from 'three';
import * as THREE from 'three';

interface SpaceshipProps {
  position?: [number, number, number];
  rotation?: [number, number, number];
}

const Spaceship = forwardRef<Group, SpaceshipProps>((props, ref) => {
  const { scene, nodes, materials } = useGLTF('/models/spaceship.glb') as any;
  const energyBeamTexture = useTexture('/textures/energy-beam-opacity.png');
  const groupRef = useRef<Group>(null);
  
  // Debug log
  useEffect(() => {
    console.log('GLTF loaded:', { scene, nodes, materials });
  }, [scene, nodes, materials]);

  // Apply alpha fix to materials
  React.useEffect(() => {
    function alphaFix(material: any) {
      material.transparent = true;
      material.alphaToCoverage = true;
      material.depthFunc = LessEqualDepth;
      material.depthTest = true;
      material.depthWrite = true;
    }
    
    if (materials.spaceship_racer) {
      alphaFix(materials.spaceship_racer);
    }
    if (materials.cockpit) {
      alphaFix(materials.cockpit);
    }
  }, [materials]);

  return (
    <group ref={ref || groupRef} {...props}>
      <group scale={0.01} rotation={[0, -Math.PI * 0.5, 0]} position={[0, 0, 0]}>
        <primitive object={scene} />
        
        {/* Energy beam effect */}
        <mesh position={[740, -60, -1350]} rotation-x={Math.PI * 0.5}>
          <cylinderGeometry args={[70, 25, 1600, 15]} />
          <meshBasicMaterial
            color={[1.0, 0.4, 0.02]}
            alphaMap={energyBeamTexture}
            transparent
            blending={CustomBlending}
            blendDst={OneFactor}
            blendEquation={AddEquation}
            side={DoubleSide}
          />
        </mesh>
      </group>
    </group>
  );
});

Spaceship.displayName = 'Spaceship';

// Preload the model
useGLTF.preload('/models/spaceship.glb');

export default Spaceship;
