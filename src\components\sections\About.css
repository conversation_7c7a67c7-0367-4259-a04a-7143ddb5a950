/* About Section */
.about-section {
  background: #0f1420;
  position: relative;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

/* About Text */
.about-text {
  padding-right: 30px;
}

.about-intro {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #cccccc;
  margin-bottom: 30px;
}

.about-details {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.detail-item {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.detail-icon {
  font-size: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  height: 50px;
}

.detail-item h4 {
  font-size: 1.2rem;
  color: #00d4ff;
  margin-bottom: 5px;
}

.detail-item p {
  color: #cccccc;
  margin: 0;
}

.detail-subtitle {
  font-size: 0.9rem;
  color: #888 !important;
}

/* About Image */
.about-image {
  position: relative;
}

.image-container {
  position: relative;
  width: 400px;
  height: 400px;
  margin: 0 auto;
}

.image-overlay {
  position: absolute;
  top: -20px;
  left: -20px;
  right: 20px;
  bottom: 20px;
  background: linear-gradient(135deg, #00d4ff, #ff00d4);
  border-radius: 20px;
  opacity: 0.3;
}

.image-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  background: #1a2030;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #00d4ff;
}

.placeholder-text {
  font-size: 80px;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4ff, #ff00d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Floating Badges */
.floating-badge {
  position: absolute;
  background: rgba(0, 212, 255, 0.2);
  border: 2px solid #00d4ff;
  border-radius: 25px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 600;
  color: #00d4ff;
}

.badge-1 {
  top: 20px;
  right: -30px;
}

.badge-2 {
  bottom: 50px;
  left: -40px;
}

.badge-3 {
  top: 50%;
  right: -50px;
}

/* Responsive */
@media (max-width: 968px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .about-text {
    padding-right: 0;
  }
  
  .image-container {
    width: 300px;
    height: 300px;
  }
  
  .floating-badge {
    display: none;
  }
}
