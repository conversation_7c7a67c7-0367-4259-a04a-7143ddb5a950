{"name": "spacecraft-react", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/free-brands-svg-icons": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^0.2.3", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@react-three/postprocessing": "^3.0.4", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.178.1", "framer-motion": "^12.23.12", "react": "^19.1.1", "react-dom": "^19.1.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "react-typed": "^2.0.12", "three": "^0.179.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}