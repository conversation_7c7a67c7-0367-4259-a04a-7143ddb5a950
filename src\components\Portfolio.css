/* Portfolio Container */
.portfolio-container {
  min-height: 100vh;
  background: #0a0f1c;
  color: #ffffff;
  overflow-x: hidden;
}

/* Section Styles */
section {
  padding: 80px 0;
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
.section-title {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 60px;
  position: relative;
}

.section-title .highlight {
  color: #00d4ff;
  position: relative;
}

.section-title .highlight::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #00d4ff, #ff00d4);
  border-radius: 2px;
}

/* Buttons */
.btn {
  padding: 12px 30px;
  border: none;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  margin: 10px;
}

.btn-primary {
  background: linear-gradient(135deg, #00d4ff, #0099ff);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
}

.btn-primary:hover {
  box-shadow: 0 6px 25px rgba(0, 212, 255, 0.6);
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: #00d4ff;
  border: 2px solid #00d4ff;
}

.btn-secondary:hover {
  background: #00d4ff;
  color: #0a0f1c;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
}

/* Responsive */
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }
  
  section {
    padding: 60px 0;
  }
}
