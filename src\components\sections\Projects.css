.projects-section {
  min-height: 100vh;
  padding: 100px 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
}

.projects-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 20% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.projects-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.projects-section .section-title {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 80px;
  color: #ffffff;
  font-weight: 700;
}

.projects-section .highlight {
  background: linear-gradient(45deg, #00d4ff, #ff00ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.projects-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 212, 255, 0.3);
}

.project-image {
  width: 100%;
  height: 250px;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.2), rgba(255, 0, 255, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(255, 0, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-image::before {
  opacity: 1;
}

.project-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.6);
}

.project-info {
  padding: 30px;
}

.project-info h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.project-info p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 1rem;
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #00d4ff;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.project-link::after {
  content: '→';
  transition: transform 0.3s ease;
}

.project-link:hover {
  color: #ff00ff;
  transform: translateX(5px);
}

.project-link:hover::after {
  transform: translateX(5px);
}

/* Featured Project Styles */
.project-card.featured {
  grid-column: span 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
}

.project-card.featured .project-image {
  height: 100%;
  min-height: 300px;
}

.project-card.featured .project-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px;
}

.project-card.featured .project-info h3 {
  font-size: 2rem;
  margin-bottom: 20px;
}

.project-card.featured .project-info p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

/* Technology Tags */
.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 20px 0;
}

.tech-tag {
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.2), rgba(255, 0, 255, 0.2));
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.4), rgba(255, 0, 255, 0.4));
  transform: translateY(-2px);
}

/* Project Actions */
.project-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.btn-project {
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  background: transparent;
  color: #ffffff;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-project.primary {
  background: linear-gradient(45deg, #00d4ff, #ff00ff);
  border-color: transparent;
}

.btn-project:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.btn-project.primary:hover {
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .projects-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .project-card.featured {
    grid-column: span 1;
    grid-template-columns: 1fr;
  }
  
  .project-card.featured .project-image {
    height: 250px;
  }
  
  .projects-section .section-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .projects-section {
    padding: 80px 0;
  }
  
  .projects-section .section-title {
    font-size: 2rem;
  }
  
  .project-info {
    padding: 25px 20px;
  }
  
  .project-card.featured .project-info {
    padding: 30px 20px;
  }
}
