import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './Projects.css';

const Projects = () => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section id="projects" className="projects-section">
      <motion.div 
        ref={ref}
        className="container"
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
      >
        <motion.h2 className="section-title" variants={itemVariants}>
          My <span className="highlight">Projects</span>
        </motion.h2>

        <div className="projects-content">
          {[1, 2, 3].map((index) => (
            <motion.div key={index} className="project-card" variants={itemVariants}>
              <div className="project-image">
                <img src={`project${index}.png`} alt={`Project ${index}`} />
              </div>
              <div className="project-info">
                <h3>Project {index}</h3>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam non euismod augue.</p>
                <a href="#" className="project-link">View Details</a>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  );
};

export default Projects;
