import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './Projects.css';

const Projects = () => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section id="projects" className="projects-section">
      <motion.div 
        ref={ref}
        className="container"
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
      >
        <motion.h2 className="section-title" variants={itemVariants}>
          My <span className="highlight">Projects</span>
        </motion.h2>

        <div className="projects-content">
          <motion.div className="project-card" variants={itemVariants}>
            <div className="project-image">
              <div className="project-placeholder">
                <span>🌐</span>
              </div>
            </div>
            <div className="project-info">
              <h3>LocalTasker - Neighborhood Service Network</h3>
              <p>Built a location-based platform connecting service providers with property owners for efficient local service matching using React, Node.js, MongoDB, and Machine learning.</p>
              <div className="tech-tags">
                <span className="tech-tag">React</span>
                <span className="tech-tag">Node.js</span>
                <span className="tech-tag">MongoDB</span>
                <span className="tech-tag">Machine Learning</span>
              </div>
              <button className="project-link" onClick={() => console.log('View LocalTasker details')}>View Details</button>
            </div>
          </motion.div>

          <motion.div className="project-card" variants={itemVariants}>
            <div className="project-image">
              <div className="project-placeholder">
                <span>🤖</span>
              </div>
            </div>
            <div className="project-info">
              <h3>AI-Powered Mock Interview System</h3>
              <p>Developed full-stack web application facilitating real-time interview experiences using Google Gemini AI for personalized questions and real-time integration.</p>
              <div className="tech-tags">
                <span className="tech-tag">React.js</span>
                <span className="tech-tag">Node.js</span>
                <span className="tech-tag">Express.js</span>
                <span className="tech-tag">MongoDB</span>
                <span className="tech-tag">Google Gemini AI</span>
              </div>
              <button className="project-link" onClick={() => console.log('View AI Interview System details')}>View Details</button>
            </div>
          </motion.div>

          <motion.div className="project-card" variants={itemVariants}>
            <div className="project-image">
              <div className="project-placeholder">
                <span>🍽️</span>
              </div>
            </div>
            <div className="project-info">
              <h3>ZeroHunger - Food Donation Platform</h3>
              <p>Developed full-stack web application facilitating food donation, connecting donors with banks and organizations. Implemented food location matching with NGO selection and donation tracking features.</p>
              <div className="tech-tags">
                <span className="tech-tag">Node.js</span>
                <span className="tech-tag">Express.js</span>
                <span className="tech-tag">Firebase</span>
                <span className="tech-tag">EJS</span>
              </div>
              <button className="project-link" onClick={() => console.log('View ZeroHunger details')}>View Details</button>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default Projects;
