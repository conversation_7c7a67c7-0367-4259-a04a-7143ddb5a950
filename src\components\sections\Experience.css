.experience-section {
  min-height: 100vh;
  padding: 100px 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
}

.experience-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 40%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.experience-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.experience-section .section-title {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 80px;
  color: #ffffff;
  font-weight: 700;
}

.experience-section .highlight {
  background: linear-gradient(45deg, #00d4ff, #ff00ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.timeline {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #00d4ff, #ff00ff);
  border-radius: 2px;
}

.timeline-item {
  position: relative;
  margin-bottom: 60px;
  width: 50%;
}

.timeline-item.left {
  left: 0;
  padding-right: 40px;
}

.timeline-item.right {
  left: 50%;
  padding-left: 40px;
}

.timeline-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 212, 255, 0.3);
}

.timeline-item.left .timeline-content::before {
  content: '';
  position: absolute;
  top: 30px;
  right: -15px;
  width: 0;
  height: 0;
  border: 15px solid transparent;
  border-left-color: rgba(255, 255, 255, 0.1);
}

.timeline-item.right .timeline-content::before {
  content: '';
  position: absolute;
  top: 30px;
  left: -15px;
  width: 0;
  height: 0;
  border: 15px solid transparent;
  border-right-color: rgba(255, 255, 255, 0.1);
}

.timeline-header h3 {
  color: #ffffff;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.timeline-header h4 {
  color: #00d4ff;
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 10px;
}

.period {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
  background: rgba(0, 212, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  display: inline-block;
}

.description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 20px 0;
  font-size: 1rem;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 20px;
}

.tech-tag {
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.2), rgba(255, 0, 255, 0.2));
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.4), rgba(255, 0, 255, 0.4));
  transform: translateY(-2px);
}

.timeline-dot {
  position: absolute;
  top: 30px;
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #00d4ff, #ff00ff);
  border-radius: 50%;
  border: 4px solid #0a0a0a;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.timeline-item.left .timeline-dot {
  right: -50px;
}

.timeline-item.right .timeline-dot {
  left: -50px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .timeline::before {
    left: 20px;
  }
  
  .timeline-item {
    width: 100%;
    left: 0 !important;
    padding-left: 60px !important;
    padding-right: 0 !important;
  }
  
  .timeline-item .timeline-content::before {
    display: none;
  }
  
  .timeline-item .timeline-dot {
    left: 10px !important;
    right: auto !important;
  }
  
  .experience-section .section-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .experience-section {
    padding: 80px 0;
  }
  
  .experience-section .section-title {
    font-size: 2rem;
  }
  
  .timeline-content {
    padding: 20px;
  }
  
  .timeline-item {
    padding-left: 50px !important;
  }
}
