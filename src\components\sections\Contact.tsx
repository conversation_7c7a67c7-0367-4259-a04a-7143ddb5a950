import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './Contact.css';

const Contact = () => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section id="contact" className="contact-section">
      <motion.div 
        ref={ref}
        className="container"
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
      >
        <motion.h2 className="section-title" variants={itemVariants}>
          Get In <span className="highlight">Touch</span>
        </motion.h2>

        <div className="contact-content">
          <motion.form className="contact-form" variants={itemVariants}>
            <div className="form-group">
              <label htmlFor="name">Your Name</label>
              <input type="text" id="name" placeholder="Enter your name" />
            </div>
            <div className="form-group">
              <label htmlFor="email">Your Email</label>
              <input type="email" id="email" placeholder="Enter your email" />
            </div>
            <div className="form-group">
              <label htmlFor="message">Your Message</label>
              <textarea id="message" placeholder="Enter your message"></textarea>
            </div>
            <motion.button 
              type="submit"
              className="btn btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Send Message
            </motion.button>
          </motion.form>

          <motion.div className="contact-info" variants={itemVariants}>
            <h4>Contact Information</h4>
            <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>Phone: <a href="tel:+919141060543">+91 9141060543</a></p>
            <p>LinkedIn: <a href="https://www.linkedin.com/in/vinayakbhat" target="_blank" rel="noopener noreferrer">LinkedIn Profile</a></p>
            <p>GitHub: <a href="https://github.com/vinayakbhat" target="_blank" rel="noopener noreferrer">GitHub Profile</a></p>
            <p>Location: Dharwad, Karnataka - 580001</p>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default Contact;

