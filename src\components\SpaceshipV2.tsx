import React, { forwardRef, useRef } from 'react';
import { useGLTF, useTexture } from '@react-three/drei';
import { Group, DoubleSide, LessEqualDepth, CustomBlending, OneFactor, AddEquation, MeshStandardMaterial } from 'three';

interface SpaceshipProps {
  position?: [number, number, number];
  rotation?: [number, number, number];
}

const SpaceshipV2 = forwardRef<Group, SpaceshipProps>((props, ref) => {
  const { nodes, materials } = useGLTF('/models/spaceship.glb') as any;
  const energyBeamTexture = useTexture('/textures/energy-beam-opacity.png');
  const groupRef = useRef<Group>(null);

  // Apply alpha fix to materials
  React.useEffect(() => {
    function alphaFix(material: any) {
      if (!material) return;
      material.transparent = true;
      material.alphaToCoverage = true;
      material.depthFunc = LessEqualDepth;
      material.depthTest = true;
      material.depthWrite = true;
    }
    
    if (materials?.spaceship_racer) {
      alphaFix(materials.spaceship_racer);
    }
    if (materials?.cockpit) {
      alphaFix(materials.cockpit);
    }
  }, [materials]);

  // Helper function to safely get material
  const getMaterial = (materialName: string) => {
    return materials?.[materialName] || new MeshStandardMaterial({ color: 'orange' });
  };

  return (
    <group ref={ref || groupRef} {...props}>
      <group scale={0.003} rotation={[0, -Math.PI * 0.5, 0]} position={[0.95, 0, -2.235]}>
        {/* Main body parts */}
        {nodes?.Cube001_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube001_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[739.26, -64.81, 64.77]}
          />
        )}
        
        {nodes?.Cylinder002_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cylinder002_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[739.69, -59.39, -553.38]}
            rotation={[Math.PI / 2, 0, 0]}
          />
        )}
        
        {nodes?.Cylinder003_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cylinder003_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[742.15, -64.53, -508.88]}
            rotation={[Math.PI / 2, 0, 0]}
          />
        )}
        
        {nodes?.Cube003_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube003_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[737.62, 46.84, -176.41]}
          />
        )}
        
        {nodes?.Cylinder004_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cylinder004_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[789.52, 59.45, -224.91]}
            rotation={[1, 0, 0]}
          />
        )}
        
        {nodes?.Cube001_RExtr001_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube001_RExtr001_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[745.54, 159.32, -5.92]}
          />
        )}
        
        {nodes?.Cube001_RPanel003_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube001_RPanel003_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[739.26, 0, 0]}
          />
        )}
        
        {nodes?.Cube001_RPanel003_RExtr_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube001_RPanel003_RExtr_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[739.26, 0, 0]}
          />
        )}
        
        {nodes?.Cube002_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube002_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[736.79, -267.14, -33.21]}
          />
        )}
        
        {nodes?.Cube001_RPanel001_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube001_RPanel001_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[739.26, 0, 0]}
          />
        )}
        
        {nodes?.Cube001_RPanel003_RExtr001_spaceship_racer_0 && (
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.Cube001_RPanel003_RExtr001_spaceship_racer_0.geometry}
            material={getMaterial('spaceship_racer')}
            position={[739.26, 0, 0]}
          />
        )}
        
        {/* Cockpit parts */}
        {nodes?.Cube005_cockpit_0 && (
          <mesh
            geometry={nodes.Cube005_cockpit_0.geometry}
            material={getMaterial('cockpit')}
            position={[739.45, 110.44, 307.18]}
            rotation={[0.09, 0, 0]}
          />
        )}
        
        {nodes?.Sphere_cockpit_0 && (
          <mesh
            geometry={nodes.Sphere_cockpit_0.geometry}
            material={getMaterial('cockpit')}
            position={[739.37, 145.69, 315.6]}
            rotation={[0.17, 0, 0]}
          />
        )}
        
        {/* Energy beam effect */}
        <mesh position={[740, -60, -1350]} rotation-x={Math.PI * 0.5}>
          <cylinderGeometry args={[70, 25, 1600, 15]} />
          <meshBasicMaterial
            color={[1.0, 0.4, 0.02]}
            alphaMap={energyBeamTexture}
            transparent
            blending={CustomBlending}
            blendDst={OneFactor}
            blendEquation={AddEquation}
            side={DoubleSide}
          />
        </mesh>
      </group>
    </group>
  );
});

SpaceshipV2.displayName = 'SpaceshipV2';

// Preload the model
useGLTF.preload('/models/spaceship.glb');

export default SpaceshipV2;
