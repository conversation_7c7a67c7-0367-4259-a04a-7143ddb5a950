import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './About.css';

const About = () => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section id="about" className="about-section">
      <motion.div 
        ref={ref}
        className="container"
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
      >
        <motion.h2 className="section-title" variants={itemVariants}>
          About <span className="highlight">Me</span>
        </motion.h2>
        
        <div className="about-content">
          <motion.div className="about-text" variants={itemVariants}>
            <p className="about-intro">
              I'm a passionate Full Stack Developer and Network Engineer with a strong 
              foundation in both software development and network infrastructure. Currently 
              pursuing my studies at Siddaganga Institute of Technology.
            </p>
            
            <div className="about-details">
              <div className="detail-item">
                <span className="detail-icon">🎓</span>
                <div>
                  <h4>Education</h4>
                  <p>B.E. in Information Science & Engineering</p>
                  <p className="detail-subtitle">Siddaganga Institute of Technology</p>
                </div>
              </div>
              
              <div className="detail-item">
                <span className="detail-icon">💡</span>
                <div>
                  <h4>Interests</h4>
                  <p>Cloud Computing, DevOps, Network Security</p>
                  <p className="detail-subtitle">AI/ML, Web Development</p>
                </div>
              </div>
              
              <div className="detail-item">
                <span className="detail-icon">🎯</span>
                <div>
                  <h4>Goal</h4>
                  <p>Building scalable and secure solutions</p>
                  <p className="detail-subtitle">Bridging the gap between development and infrastructure</p>
                </div>
              </div>
            </div>
          </motion.div>
          
          <motion.div className="about-image" variants={itemVariants}>
            <div className="image-container">
              <div className="image-overlay"></div>
              <div className="image-placeholder">
                <span className="placeholder-text">VB</span>
              </div>
            </div>
            
            <motion.div 
              className="floating-badge badge-1"
              animate={{ y: [0, -10, 0] }}
              transition={{ repeat: Infinity, duration: 3 }}
            >
              <span>React</span>
            </motion.div>
            
            <motion.div 
              className="floating-badge badge-2"
              animate={{ y: [0, 10, 0] }}
              transition={{ repeat: Infinity, duration: 3, delay: 1 }}
            >
              <span>Node.js</span>
            </motion.div>
            
            <motion.div 
              className="floating-badge badge-3"
              animate={{ y: [0, -10, 0] }}
              transition={{ repeat: Infinity, duration: 3, delay: 2 }}
            >
              <span>CCNA</span>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default About;
