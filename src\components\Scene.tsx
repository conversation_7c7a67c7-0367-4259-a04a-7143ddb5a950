import React, { useRef, useState, useEffect, useMemo, Suspense } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera } from '@react-three/drei';
import {
  Vector3,
  Vector2,
  Raycaster,
  Mesh,
  PlaneGeometry
} from 'three';
import { EffectComposer, Bloom } from '@react-three/postprocessing';
import SpaceshipV2 from './SpaceshipV2';
import Stars from './Stars';

function Scene() {
  const { camera } = useThree();
  const spaceshipRef = useRef<any>(null);
  const [intersectionPoint, setIntersectionPoint] = useState<Vector3 | null>(null);
  const translY = useRef(0);
  const translAcceleration = useRef(0);
  const angleZ = useRef(0);
  const angleAcceleration = useRef(0);

  // Create invisible plane for mouse interaction
  const invisiblePlane = useMemo(() => {
    const geometry = new PlaneGeometry(20, 20);
    const mesh = new Mesh(geometry);
    return mesh;
  }, []);

  // Mouse movement handler
  useEffect(() => {
    const raycaster = new Raycaster();
    const pointer = new Vector2();

    function onPointerMove(event: PointerEvent) {
      pointer.x = (event.clientX / window.innerWidth) * 2 - 1;
      pointer.y = -(event.clientY / window.innerHeight) * 2 + 1;

      raycaster.setFromCamera(pointer, camera);
      const intersects = raycaster.intersectObject(invisiblePlane);
      const point = intersects[0]?.point;

      if (point) {
        // Prevent spring motion differences across x axis
        point.x = 3;
        setIntersectionPoint(point);
      }
    }

    window.addEventListener('pointermove', onPointerMove);
    return () => {
      window.removeEventListener('pointermove', onPointerMove);
    };
  }, [camera, invisiblePlane]);

  // Animation loop
  useFrame(() => {
    if (intersectionPoint && spaceshipRef.current) {
      const targetY = intersectionPoint.y || 0;
      translAcceleration.current += (targetY - translY.current) * 0.002; // stiffness
      translAcceleration.current *= 0.95; // damping
      translY.current += translAcceleration.current;

      const dir = intersectionPoint.clone().sub(new Vector3(0, translY.current, 0)).normalize();
      const dirCos = dir.dot(new Vector3(0, 1, 0));
      const angle = Math.acos(dirCos) - Math.PI * 0.5;
      angleAcceleration.current += (angle - angleZ.current) * 0.01; // stiffness
      angleAcceleration.current *= 0.85; // damping
      angleZ.current += angleAcceleration.current;

      // Update spaceship position and rotation
      spaceshipRef.current.position.y = translY.current;
      spaceshipRef.current.rotation.z = angleZ.current;
      spaceshipRef.current.rotation.x = angleZ.current;
      spaceshipRef.current.rotation.order = 'ZXY';
    }

    // Temporarily disable environment map for debugging
    /*
    if (spaceshipRef.current) {
      if (envMapRT.current) envMapRT.current.dispose();
      
      spaceshipRef.current.visible = false;
      scene.background = null;
      envMapRT.current = pmrem.fromScene(scene, 0, 0.1, 1000);
      scene.background = new Color('#598889').multiplyScalar(0.05);
      spaceshipRef.current.visible = true;

      spaceshipRef.current.traverse((child: any) => {
        if (child?.material?.envMapIntensity !== undefined) {
          child.material.envMap = envMapRT.current?.texture;
          child.material.envMapIntensity = 100;
          if (child.material.normalScale) {
            child.material.normalScale.set(0.3, 0.3);
          }
        }
      });
    }
    */
  });

  return (
    <>
      <PerspectiveCamera makeDefault position={[-5, 6, 10]} fov={25} />
      <OrbitControls enableDamping target={[0, 0, 0]} />
      
      <ambientLight intensity={0.5} />
      <directionalLight 
        intensity={1.8} 
        position={[0, 10, 0]} 
        castShadow 
        shadow-bias={-0.0001} 
      />
      
      <Suspense fallback={null}>
        <SpaceshipV2 ref={spaceshipRef} />
        <Stars />
      </Suspense>
      
      <EffectComposer>
        <Bloom 
          intensity={0.275}
          luminanceThreshold={1}
          luminanceSmoothing={0}
          radius={1}
          levels={9}
          mipmapBlur
        />
      </EffectComposer>
    </>
  );
}

export default Scene;
