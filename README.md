# Spacecraft 3D Model - React Version

This is a React/TypeScript conversion of the original Svelte-based spacecraft 3D model project.

## Original Project
The original project was built with <PERSON><PERSON><PERSON><PERSON> (Three.js + Svelte) and can be found at:
https://github.com/Domenicobrz/Threlte-in-practice-spaceship

## Technologies Used
- React with TypeScript
- React Three Fiber (Three.js for React)
- @react-three/drei (useful helpers for React Three Fiber)
- @react-three/postprocessing (post-processing effects)

## Features
- Interactive 3D spacecraft model that follows mouse movements
- Particle stars animation
- Bloom post-processing effect
- Environment mapping for realistic reflections
- Energy beam effect

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation
```bash
npm install
```

### Running the Development Server
```bash
npm start
```

The app will run on `http://localhost:3000`

### Building for Production
```bash
npm run build
```

## Project Structure
```
src/
├── components/
│   ├── Scene.tsx       # Main scene with camera, lights, and effects
│   ├── Spaceship.tsx   # Spacecraft model component
│   └── Stars.tsx       # Animated stars background
├── App.tsx             # Main app component with Canvas
├── App.css             # Global styles
└── index.tsx           # Entry point

public/
├── models/
│   └── spaceship.glb   # 3D model file
└── textures/
    ├── energy-beam-opacity.png
    └── star.png
```

## Key Differences from Svelte Version
- Uses React hooks instead of Svelte's reactive declarations
- React Three Fiber's `useFrame` instead of Threlte's `useRender`
- TypeScript interfaces for better type safety
- React's `forwardRef` for ref forwarding
- Different post-processing setup using @react-three/postprocessing

## Credits
- Original Svelte implementation by Domenicobrz
- 3D Model: "Rusty Spaceship - Orange" by Sousinho (CC-BY-4.0 license)

## License
This project maintains the same license as the original implementation.
