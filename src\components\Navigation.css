/* Navigation Styles */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 15, 28, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  padding: 20px 0;
}

.navigation-scrolled {
  padding: 15px 0;
  background: rgba(10, 15, 28, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Logo */
.nav-logo {
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.logo-text {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4ff, #ff00d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
}

.logo-subtitle {
  font-size: 12px;
  color: #888;
  margin-top: 2px;
}

/* Menu */
.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}

.nav-link {
  background: none;
  border: none;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
}

.nav-link.active {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.2);
}

.nav-icon {
  font-size: 20px;
}

.nav-label {
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .nav-menu {
    gap: 15px;
  }
  
  .nav-label {
    display: none;
  }
  
  .nav-link {
    padding: 8px 12px;
  }
}
