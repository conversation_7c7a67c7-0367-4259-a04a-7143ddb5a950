import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './Experience.css';

const Experience = () => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  });

  const experiences = [
    {
      title: "Web Developer",
      company: "IECEIT-2025 Website Development",
      period: "2025",
      description: "Built the official website for International Conference on Emerging Computation and Information Technologies (IECEIT-2025) at Siddaganga Institute of Technology, Tumkur.",
      technologies: ["Web Development", "Conference Website", "Responsive Design"]
    },
    {
      title: "Video Editing Lead",
      company: "Team Quest",
      period: "2023 - Present",
      description: "Video Editing Head of Team Quest, the only club recognized by Training & Placement department, helping students through Quest Test Series (QTS).",
      technologies: ["Video Editing", "Content Creation", "Team Leadership", "Educational Content"]
    },
    {
      title: "Student",
      company: "Siddaganga Institute of Technology",
      period: "2022 - 2026",
      description: "Pursuing B.E. in Computer Science and Engineering. Active participant in hackathons and technical events. Solved 200+ DSA problems from LeetCode and GeeksForGeeks.",
      technologies: ["Data Structures", "Algorithms", "Problem Solving", "Computer Science"]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section id="experience" className="experience-section">
      <motion.div 
        ref={ref}
        className="container"
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
      >
        <motion.h2 className="section-title" variants={itemVariants}>
          Work <span className="highlight">Experience</span>
        </motion.h2>

        <div className="timeline">
          {experiences.map((exp, index) => (
            <motion.div 
              key={index}
              className={`timeline-item ${index % 2 === 0 ? 'left' : 'right'}`}
              variants={itemVariants}
            >
              <div className="timeline-content">
                <div className="timeline-header">
                  <h3>{exp.title}</h3>
                  <h4>{exp.company}</h4>
                  <span className="period">{exp.period}</span>
                </div>
                <p className="description">{exp.description}</p>
                <div className="tech-stack">
                  {exp.technologies.map((tech, i) => (
                    <span key={i} className="tech-tag">{tech}</span>
                  ))}
                </div>
              </div>
              <div className="timeline-dot"></div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  );
};

export default Experience;
