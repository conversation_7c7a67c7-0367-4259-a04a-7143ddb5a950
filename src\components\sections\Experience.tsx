import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './Experience.css';

const Experience = () => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  });

  const experiences = [
    {
      title: "Full Stack Developer Intern",
      company: "Tech Solutions Inc.",
      period: "June 2023 - Present",
      description: "Developed and maintained web applications using React and Node.js. Implemented RESTful APIs and worked with MongoDB databases.",
      technologies: ["React", "Node.js", "MongoDB", "Express.js"]
    },
    {
      title: "Network Administrator Assistant",
      company: "NetWorks Corp",
      period: "Jan 2023 - May 2023",
      description: "Assisted in managing and configuring network infrastructure. Worked with Cisco routers and switches, implemented VLANs and network security protocols.",
      technologies: ["Cisco IOS", "Network Security", "VLAN", "TCP/IP"]
    },
    {
      title: "Freelance Web Developer",
      company: "Self-Employed",
      period: "2022 - Present",
      description: "Created responsive websites for small businesses. Focused on modern design principles and SEO optimization.",
      technologies: ["HTML/CSS", "JavaScript", "WordPress", "SEO"]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section id="experience" className="experience-section">
      <motion.div 
        ref={ref}
        className="container"
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
      >
        <motion.h2 className="section-title" variants={itemVariants}>
          Work <span className="highlight">Experience</span>
        </motion.h2>

        <div className="timeline">
          {experiences.map((exp, index) => (
            <motion.div 
              key={index}
              className={`timeline-item ${index % 2 === 0 ? 'left' : 'right'}`}
              variants={itemVariants}
            >
              <div className="timeline-content">
                <div className="timeline-header">
                  <h3>{exp.title}</h3>
                  <h4>{exp.company}</h4>
                  <span className="period">{exp.period}</span>
                </div>
                <p className="description">{exp.description}</p>
                <div className="tech-stack">
                  {exp.technologies.map((tech, i) => (
                    <span key={i} className="tech-tag">{tech}</span>
                  ))}
                </div>
              </div>
              <div className="timeline-dot"></div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  );
};

export default Experience;
