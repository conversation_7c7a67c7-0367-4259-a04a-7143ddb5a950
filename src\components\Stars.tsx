import React, { useMemo, useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { InstancedMesh, Color, Vector3, DoubleSide, Object3D } from 'three';
import { useTexture } from '@react-three/drei';

interface Star {
  pos: Vector3;
  len: number;
  speed: number;
  rad: number;
  color: Color;
}

const STARS_COUNT = 350;
const colors = ['#fcaa67', '#C75D59', '#ffffc7', '#8CC5C6', '#A5898C'];

function r(min: number, max: number): number {
  const diff = Math.random() * (max - min);
  return min + diff;
}

function resetStar(star: Star): Star {
  if (r(0, 1) > 0.8) {
    star.pos = new Vector3(r(-10, -30), r(-5, 5), r(6, -6));
    star.len = r(1.5, 15);
  } else {
    star.pos = new Vector3(r(-15, -45), r(-10.5, 1.5), r(30, -45));
    star.len = r(2.5, 20);
  }

  star.speed = r(19.5, 42);
  star.rad = r(0.04, 0.07);
  star.color = new Color(colors[Math.floor(Math.random() * colors.length)])
    .convertSRGBToLinear()
    .multiplyScalar(1.3);

  return star;
}

function Stars() {
  const meshRef = useRef<InstancedMesh>(null);
  const starTexture = useTexture('/textures/star.png');
  const tempObject = useMemo(() => new Object3D(), []);

  // Initialize stars
  const stars = useMemo(() => {
    const starsArray: Star[] = [];
    for (let i = 0; i < STARS_COUNT; i++) {
      const star: Star = {
        pos: new Vector3(),
        len: 0,
        speed: 0,
        rad: 0,
        color: new Color()
      };
      starsArray.push(resetStar(star));
    }
    return starsArray;
  }, []);

  // Set initial positions
  useMemo(() => {
    if (!meshRef.current) return;
    
    stars.forEach((star, i) => {
      tempObject.position.copy(star.pos);
      tempObject.scale.set(star.len, 1, 1);
      tempObject.updateMatrix();
      meshRef.current!.setMatrixAt(i, tempObject.matrix);
      meshRef.current!.setColorAt(i, star.color);
    });
    
    if (meshRef.current.instanceColor) {
      meshRef.current.instanceColor.needsUpdate = true;
    }
    meshRef.current.instanceMatrix.needsUpdate = true;
  }, [stars, tempObject]);

  // Animation
  useFrame((state, delta) => {
    if (!meshRef.current) return;

    stars.forEach((star, i) => {
      star.pos.x += star.speed * delta;
      if (star.pos.x > 40) {
        resetStar(star);
      }

      tempObject.position.copy(star.pos);
      tempObject.scale.set(star.len, 1, 1);
      tempObject.updateMatrix();
      meshRef.current!.setMatrixAt(i, tempObject.matrix);
      meshRef.current!.setColorAt(i, star.color);
    });

    if (meshRef.current.instanceColor) {
      meshRef.current.instanceColor.needsUpdate = true;
    }
    meshRef.current.instanceMatrix.needsUpdate = true;
  });

  return (
    <instancedMesh ref={meshRef} args={[undefined, undefined, STARS_COUNT]}>
      <planeGeometry args={[1, 0.05]} />
      <meshBasicMaterial 
        side={DoubleSide} 
        alphaMap={starTexture} 
        transparent 
      />
    </instancedMesh>
  );
}

export default Stars;
